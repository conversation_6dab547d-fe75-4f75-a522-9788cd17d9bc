window.onload = function () {
    const canvas = document.getElementById("logoCanvas");
    if (!canvas) return;
    const ctx = canvas.getContext("2d");

    // 设置绘制样式
    ctx.lineWidth = 2;
    ctx.strokeStyle = "black";




    // 画云朵（在上层，避免被遮挡）
    const cloudOffsetY = 43; // 设置云朵向下位移的偏移量（30像素）
    const cloudOffsetX = -20; // 设置云朵向左位移的偏移量（40像素）

    ctx.beginPath();
    ctx.arc(40 + cloudOffsetX, 30 + cloudOffsetY, 15, Math.PI * 0.5, Math.PI * 1.5);
    ctx.arc(60 + cloudOffsetX, 20 + cloudOffsetY, 20, Math.PI * 1, Math.PI * 2);
    ctx.arc(100 + cloudOffsetX, 10 + cloudOffsetY, 15, Math.PI * 1.3, Math.PI * 2.3);
    ctx.arc(120 + cloudOffsetX, 28 + cloudOffsetY, 13, Math.PI * 1.5, Math.PI * 2.5);
    ctx.quadraticCurveTo(158 + cloudOffsetX, 66 + cloudOffsetY, 90 + cloudOffsetX, 67 + cloudOffsetY);
    ctx.quadraticCurveTo(0 + cloudOffsetX, 68 + cloudOffsetY, 25 + cloudOffsetX, 40 + cloudOffsetY);
    ctx.closePath();

    // 填充白色
    ctx.fillStyle = "white";
    ctx.fill();
    // 绘制黑色边框
    ctx.stroke();



    // 画第二个对话框（右下，箭头向左上）
    ctx.beginPath();
    ctx.moveTo(50, 20);  // 确保从圆角后的位置开始
    ctx.lineTo(110, 20);
    ctx.quadraticCurveTo(120, 20, 120, 30);
    ctx.lineTo(120, 60);
    ctx.quadraticCurveTo(120, 70, 110, 70);
    ctx.lineTo(70, 70);
    ctx.lineTo(55, 85);
    ctx.lineTo(55, 70);
    ctx.lineTo(50, 70);
    ctx.quadraticCurveTo(40, 70, 40, 60);
    ctx.lineTo(40, 30);
    ctx.quadraticCurveTo(40, 20, 50, 20);
    ctx.closePath();

    // 填充白色
    ctx.fillStyle = "white";
    ctx.fill();
    // 绘制黑色边框
    ctx.stroke();
    // 设置字体并绘制BLOG文本
    ctx.font = "bold 15px Arial";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillStyle = "black"; // 设置字体颜色为黑色
    ctx.fillText("BLOG", 79, 50);

    // 设置字体并绘制LOGO DESIGN文本
    ctx.font = "12px Arial";
    ctx.fillStyle = "red";
    ctx.fillText("SHARING LIFE", 60, 100);
};
