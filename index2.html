<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>社团管理</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #e0c1c1;
            color: #333;
        }

        header {
            background-color: #bc523a;
            color: white;
            padding: 1rem;
            text-align: center;
        }

        nav ul {
            list-style-type: none;
            padding: 0;
        }

        nav ul li {
            display: inline;
            margin-right: 15px;
        }

        nav ul li a {
            color: #ffffff;
            text-decoration: none;
            font-weight: bold;
        }

        aside ul li a {
            color: #2b4046;
            text-decoration: none;
            font-weight: bold;
        }

        main {
            max-width: 1200px;
            margin: 20px auto;
            background-color: white;
            padding: 20px;
            display: flex;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            justify-content: space-between;
        }

        article {
            flex: 3;
            margin-right: 20px;
        }

        aside {
            flex: 1;
            background-color: #ecf0f1;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            position: relative;
            text-align: center;
            /* 居中显示 */
        }

        h1,
        h2,
        h3 {
            color: #943310bc;
        }

        figure {
            margin: 20px 0;
            text-align: center;
        }

        img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
        }

        figcaption {
            font-style: italic;
            color: #7f8c8d;
            margin-top: 10px;
        }

        video {
            max-width: 100%;
            height: auto;
        }

        audio {
            margin-top: 20px;
        }

        footer {
            background-color: #9c4a2c;
            color: white;
            text-align: center;
            padding: 1rem;
            margin-top: 20px;
        }

        /* 使form位于右下角并居中 */
        form {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            /* 让表单宽度更好适应aside */
        }

        input[type="text"],
        input[type="email"],
        input[type="tel"],
        input[type="date"],
        input[type="file"],
        input[type="submit"] {
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
            width: 90%;
            /* 让输入框在表单中全宽显示 */
        }

        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }

        input[type="submit"]:hover {
            background-color: #45a049;
        }
    </style>
</head>

<body>
    <header>
        <h1>社团管理</h1>
        <nav>
            <ul>
                <li><a href="#">社团简介</a></li>
                <li><a href="#">社团部门</a></li>
                <li><a href="#">义工活动</a></li>
                <li><a href="#">常用查询</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <article>
            <h2>郁金香志愿服务队</h2>
            <p>发布日期：<time datetime="2024-03-15">2024年3月15日</time></p>
            <figure>
                <img src="图标.png" alt="郁金香志愿者服务队" width="600" height="400">
                <figcaption>紫色的郁金香代表大爱，代表软件学院大学生志愿者将以一颗诚挚的心，将无私的爱奉献给需要之处，服务没有止境，就像郁金香的花语一样，软件学院学生志愿者将以无尽的爱服务社会，奉献爱心。
                </figcaption>
            </figure>
            <section>
                <h3>部门职能</h3>
                <p><mark>活动组</mark>主要负责各项常规活动的开展，义工的招募，以及各项大型活动的前期跟进，中期执行以及后期材料的整合。
                </p>
                <p><mark>策划组</mark>负责策划部门的大型活动，以及策划部门内的一些友谊活动，加强大家的联系，促进部门的和谐风气。</p>
                <p><mark>宣传组</mark>主要负责软件郁金香微信公众号的运营， 义工活动音像采集，推文制作，以及活动视频剪辑。</p>

            </section>
            <section>
                <h3>更多讯息</h3>
                <details>
                    <summary>点击进一步了解郁金香社团</summary>
                    <p>郁金香志愿服务队诞生于2012年5月1日，自成立以来一直秉承着“奉献，友爱，互助，进步”的志愿服务精神。</p>
                </details>
            </section>

            <section>
                <h2>社团活动播报</h2>
                <video width="560" height="315" controls>
                    <source src="1.mp4" type="video/mp4">
                </video>
                <audio controls>
                    <source src="1.mp3" type="audio/mp3">
                </audio>
            </section>
        </article>

        <aside>
            <h2>最新义工活动</h2>
            <ul>
                <li>
                    <a href="#">“摊”开未来，“面”见梦想！！！</a>
                    <br><span style="color: #888;">义工招募进度：</span>
                    <progress value="70" max="100"></progress>
                    70%
                </li>
                <li>
                    <a href="#">共赴纳新之约，再续招协之缘</a>
                    <br><span style="color: #888;">义工招募进度：</span>
                    <progress value="50" max="100"></progress>
                    50%
                </li>
                <li>
                    <a href="#">梦想启航，设计未来！</a>
                    <br><span style="color: #888;">义工招募进度：</span>
                    <progress value="85" max="100"></progress>
                    85%
                </li>
                <li>
                    <a href="#">唱诵美诗，我有华章！</a>
                    <br><span style="color: #888;">义工招募进度：</span>
                    <progress value="30" max="100"></progress>
                    30%
                </li>
            </ul>
            表单部分，位于aside的右下角 -->
            <form action="#">
                <h2>义工信息收集</h2>
                请输入您的义工号：<input type="text" name="username" autofocus required></br>
                请输入您的邮箱：<input type="email" name="email" required></br>
                请输入您的电话号码：<input type="tel" pattern="\d{3}-\d{3}-\d{4}" placeholder="格式：xxx-xxx-xxxx" required></br>
                活动服务日期：<input type="date" / required></br>
                个人资料上传：<input type="file" name="file" multiple></br>
                <input type="submit" value="提交">
            </form>
        </aside>
    </main>

    <footer>
        <p>&copy; 2024 深圳信息职业技术学院. 保留所有权利。</p>
    </footer>
</body>

</html>