<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="一个包含日常和学术内容的个人博客网站">
    <title>我的博客空间 - 无障碍博客平台</title>
    <style>
        :root {
            --primary-color: #2b5a99;
            /* 提高颜色对比度 */
            --secondary-color: #f5f5f5;
            --text-color: #333;
            --focus-outline: 3px solid #ffd700;
            --link-color: #0066cc;
            --link-hover-color: #003366;
            --success-color: #28a745;
            --error-color: #dc3545;
            --warning-color: #ffc107;
        }

        /* 添加主题切换样式 */
        [data-theme="dark"] {
            --primary-color: #375a7f;
            --secondary-color: #2c3e50;
            --text-color: #ecf0f1;
            --link-color: #3498db;
            --link-hover-color: #2980b9;
        }

        [data-theme="high-contrast"] {
            --primary-color: #000000;
            --secondary-color: #ffffff;
            --text-color: #000000;
            --link-color: #0000EE;
            --link-hover-color: #551A8B;
        }

        /* 字体大小控制 */
        .font-size-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--secondary-color);
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        /* 添加无障碍辅助类 */
        .visually-hidden {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            border: 0;
        }

        /* 增强文本可读性 */
        .blog-card p {
            max-width: 70ch;
            line-height: 1.8;
            margin-bottom: 1.5em;
        }

        /* 添加焦点指示器动画 */
        @media (prefers-reduced-motion: no-preference) {
            *:focus {
                outline: var(--focus-outline);
                outline-offset: 2px;
                transition: outline-offset 0.2s ease;
            }
        }

        /* 添加主题切换按钮样式 */
        .theme-switcher {
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--secondary-color);
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        /* 重置焦点样式 */
        *:focus {
            outline: var(--focus-outline);
            outline-offset: 2px;
        }

        /* 跳过导航链接 */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 0;
            background: var(--primary-color);
            color: white;
            padding: 8px;
            z-index: 100;
            transition: top 0.3s;
        }

        .skip-link:focus {
            top: 0;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            color: var(--text-color);
            font-size: 16px;
            /* 确保基础字体大小合适 */
        }

        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            text-align: center;
        }

        .nav {
            background-color: var(--secondary-color);
            padding: 1rem;
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            /* 支持响应式布局 */
        }

        .nav button {
            padding: 0.8rem 1.5rem;
            border: 2px solid transparent;
            border-radius: 4px;
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
            font-size: 1.1rem;
            min-width: 120px;
            /* 确保按钮足够大，易于点击 */
        }

        .nav button:hover,
        .nav button:focus {
            background-color: #1a365d;
            border-color: #ffd700;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .blog-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .blog-card {
            border: 1px solid #ddd;
            padding: 1.5rem;
            border-radius: 8px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .blog-card h2 {
            color: var(--primary-color);
            margin-top: 0;
            font-size: 1.5rem;
        }

        .blog-card img {
            max-width: 100%;
            height: auto;
            alt: "博客配图";
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            :root {
                --primary-color: #000000;
                --text-color: #000000;
                --secondary-color: #ffffff;
            }

            .blog-card {
                border: 2px solid black;
            }
        }

        /* 减少动画效果 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation: none !important;
                transition: none !important;
            }
        }

        /* 响应式文字大小 */
        @media screen and (max-width: 768px) {
            body {
                font-size: 18px;
            }

            .blog-card {
                font-size: 1.1rem;
            }
        }

        /* 一键阅读 */
        /* #readButton {
    position: fixed;
    top: 20px;
    right: calc(10%); */
        /* 100% - (字体控制div的宽度 + 10px间距) */
        /* background: var(--secondary-color);
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    cursor: pointer;
} */

        /* .font-size-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--secondary-color);
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
} */
        /* 浮窗样式 */
        .content-preview {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1001;
            max-width: 80%;
            width: 80%;
            overflow-y: auto;
        }

        .content-preview h3 {
            margin-top: 0;
        }

        .content-preview input[type="text"],
        .content-preview textarea {
            width: 100%;
            padding: 0.8rem 1.5rem;
            border: 2px solid transparent;
            border-radius: 4px;
            background-color: #f5f5f5;
            color: var(--text-color);
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .content-preview button {
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }

        .content-preview button:hover,
        .content-preview button:focus {
            background-color: #1a365d;
        }

        .content-preview .close-button {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
        }

        .content-preview .close-button:hover,
        .content-preview .close-button:focus {
            color: var(--primary-color);
        }

        /* 添加按钮样式 */
        #addButton {
            background-color: var(--primary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }

        #addButton:hover {
            background-color: #1a365d;
        }

        .editButton {
            background-color: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .editButton:hover,
        .editButton:focus {
            background-color: #1a365d;
        }

        /* 搜索框样式 */
        #searchInput {
            width: 80%;
            max-width: 400px;
            padding: 0.8rem 1.5rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        #searchInput:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        /* 删除按钮样式 */
        .deleteButton {
            background-color: var(--error-color);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .deleteButton:hover {
            background-color: #c82333;
        }
    </style>
</head>

<body>
    <header class="header" role="banner">
        <h1 id="site-title">我的博客空间</h1>
        <!-- 新增博客按钮 -->
        <button id="addButton">添加新博客</button>

        <!-- 搜索框 -->
        <input type="text" id="searchInput" placeholder="搜索博客...">
    </header>
    <nav class="nav" role="navigation" aria-label="主导航">
        <button aria-label="显示日常博客" onclick="blogManager.showCategory('daily')" aria-pressed="true" id="daily-button">
            日常博客
        </button>
        <button aria-label="显示学术博客" onclick="blogManager.showCategory('academic')" aria-pressed="false"
            id="academic-button">
            学术博客
        </button>
    </nav>
    <main id="main-content" class="main-content" role="main" tabindex="-1">
        <div id="blogContainer" class="blog-container" aria-live="polite"></div>
    </main>
    <div class="font-size-controls" role="group" aria-label="字体大小控制">
        <button onclick="changeFontSize('decrease')" aria-label="减小字体">A-</button>
        <button onclick="changeFontSize('reset')" aria-label="重置字体">A</button>
        <button onclick="changeFontSize('increase')" aria-label="增大字体">A+</button>
    </div>

    <div class="theme-switcher" role="group" aria-label="一键阅读页面">
        <button id="readButton" onclick="readPageContent()" aria-label="一键阅读页面">一键阅读页面</button>
    </div>

    <div id="contentPreview" class="content-preview visually-hidden" role="dialog" aria-modal="true"
        aria-labelledby="preview-title" aria-describedby="preview-description">
        <h3 id="preview-title">博客内容</h3>
        <input type="hidden" id="operationType" value="add"> <!-- 添加操作类型 -->
        <input type="hidden" id="blogId" value=""> <!-- 添加博客ID -->
        <input type="text" id="previewBlogInput" placeholder="请输入新的博客标题">
        <textarea id="previewBlogContent" placeholder="请输入新的博客内容"></textarea>
        <button id="previewAddButton">添加/保存</button>
        <button class="close-button" id="closePreview" aria-label="关闭预览">&times;</button>
    </div>

    <!-- 添加canvas元素用于显示logo -->
    <canvas id="logoCanvas" width="120" height="120" style="position: absolute; top: 10px; left: 10px;">
    </canvas>
    <script src="logo.js"></script>
    <canvas id="donationCanvas" width="250" height="300" style="position: absolute; top: 300px; left: 20px;">
    </canvas>
    <script src="donation.js"></script>

    <canvas id="clockCanvas" width="150" height="150"
        style="position: absolute; top: 10px; right: 200px; z-index: 1001;">
    </canvas>
    <script src="clock.js"></script><!-- 引入时钟脚本 -->
    <script>
        // 原有的 Blog 和 BlogManager 类保持不变
        class Blog {
            constructor(id, title, content, category, date, author) {
                this.id = id;
                this.title = title;
                this.content = content;
                this.category = category;
                this.date = date;
                this.author = author;
            }

            createHTML() {
                return `
                    <article class="blog-card" role="article" tabindex="0">
                        <h2 id="blog-${this.id}">${this.title}</h2>
                        <div aria-labelledby="blog-${this.id}">
                            <p>${this.content}</p>
                            <footer>
                                <p aria-label="作者信息">作者: ${this.author}</p>
                                <p aria-label="发布日期">发布日期: ${this.date}</p>
                                <button class="editButton" aria-label="修改此博客">修改</button>
                                <button class="deleteButton" aria-label="删除此博客">删除</button>
                            </footer>
                        </div>
                    </article>
                `;
            }
        }

        class BlogManager {
            constructor() {
                this.blogs = [];
                this.initializeBlogs();
                this.setupKeyboardNavigation();
            }

            initializeBlogs() {
                this.blogs = [
                    // 日常博客
                    new Blog(1, "我的旅行日记", "今天去了长城，感受到了中国历史的磅礴。站在长城上，望着蜿蜒起伏的城墙，不禁感叹古人的智慧...", "daily", "2024-03-20", "张三"),
                    new Blog(2, "美食探店记", "发现了一家非常地道的川菜馆，他们的麻婆豆腐和水煮鱼都非常正宗...", "daily", "2024-03-18", "张三"),
                    new Blog(3, "城市摄影札记", "清晨的城市有着不一样的美，薄雾中的建筑轮廓若隐若现...", "daily", "2024-03-16", "李四"),
                    new Blog(4, "读书笔记：《百年孤独》", "马尔克斯的魔幻现实主义手法让人着迷...", "daily", "2024-03-15", "王五"),
                    new Blog(5, "园艺日记", "今天在阳台种下了第一批蔬菜种子，期待它们的生长...", "daily", "2024-03-14", "赵六"),
                    new Blog(6, "运动健身记录", "坚持跑步一个月后，明显感觉体能提升了...", "daily", "2024-03-13", "张三"),

                    // 学术博客
                    new Blog(7, "机器学习算法研究", "本文将探讨深度学习在图像识别中的应用，特别是卷积神经网络的最新进展...", "academic", "2024-03-19", "李四"),
                    new Blog(8, "量子计算研究进展", "量子比特的相干时间突破新记录，为实用量子计算机的实现带来希望...", "academic", "2024-03-17", "王五"),
                    new Blog(9, "环境科学研究", "全球变暖对海洋生态系统的影响研究报告...", "academic", "2024-03-15", "赵六"),
                    new Blog(10, "人工智能伦理探讨", "随着AI技术的发展，我们需要认真考虑相关的伦理问题...", "academic", "2024-03-13", "张三"),
                    new Blog(11, "可持续能源发展", "太阳能发电效率优化研究的最新突破...", "academic", "2024-03-11", "李四"),
                    new Blog(12, "脑科学研究前沿", "使用fMRI研究人类决策机制的新发现...", "academic", "2024-03-09", "王五")
                ];
            }

            showCategory(category) {
                const container = document.getElementById('blogContainer');
                container.innerHTML = '';

                // 更新按钮状态
                document.getElementById('daily-button').setAttribute('aria-pressed', category === 'daily');
                document.getElementById('academic-button').setAttribute('aria-pressed', category === 'academic');

                const filteredBlogs = this.blogs.filter(blog => blog.category === category);

                // 添加分类标题用于屏幕阅读器
                container.innerHTML = `<h2 class="visually-hidden">${category === 'daily' ? '日常' : '学术'}博客列表</h2>`;

                filteredBlogs.forEach(blog => {
                    container.innerHTML += blog.createHTML();
                });

                // 发送通知给屏幕阅读器
                this.announceContentChange(`已加载${category === 'daily' ? '日常' : '学术'}博客内容`);
            }

            setupKeyboardNavigation() {
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                        const currentCategory = document.getElementById('daily-button').getAttribute('aria-pressed') === 'true' ? 'daily' : 'academic';
                        this.showCategory(currentCategory === 'daily' ? 'academic' : 'daily');
                        e.preventDefault();
                    }
                });
            }

            announceContentChange(message) {
                const announcer = document.createElement('div');
                announcer.setAttribute('aria-live', 'polite');
                announcer.setAttribute('class', 'visually-hidden');
                announcer.textContent = message;
                document.body.appendChild(announcer);
                setTimeout(() => announcer.remove(), 1000);
            }

            addBlog(blog, position = 0) {
                this.blogs.splice(position, 0, blog);
                this.showCategory(blog.category);
            }
        }

        const blogManager = new BlogManager();
        blogManager.showCategory('daily');

        // 添加字体大小控制功能
        function changeFontSize(action) {
            const root = document.documentElement;
            const currentSize = parseFloat(getComputedStyle(root).fontSize);

            switch (action) {
                case 'increase':
                    root.style.fontSize = `${currentSize * 1.2}px`;
                    break;
                case 'decrease':
                    root.style.fontSize = `${currentSize * 0.8}px`;
                    break;
                case 'reset':
                    root.style.fontSize = '16px';
                    break;
            }

            // 通知屏幕阅读器
            announceChange(`字体大小已${action === 'increase' ? '增大' : action === 'decrease' ? '减小' : '重置'}`);
        }

        // 添加主题切换功能
        function changeTheme(theme) {
            document.body.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);

            // 通知屏幕阅读器
            announceChange(`已切换到${theme === 'light' ? '普通' : theme === 'dark' ? '深色' : '高对比度'}主题`);
        }

        // 通用的屏幕阅读器通知函数
        function announceChange(message) {
            const announcer = document.createElement('div');
            announcer.setAttribute('aria-live', 'polite');
            announcer.setAttribute('class', 'visually-hidden');
            announcer.textContent = message;
            document.body.appendChild(announcer);
            setTimeout(() => announcer.remove(), 1000);
        }

        // 初始化主题
        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme') || 'light';
            changeTheme(savedTheme);
        });

        // 添加一键阅读功能
        function readPageContent() {
            let textContent = document.body.innerText;

            const images = document.querySelectorAll('img');
            images.forEach(img => {
                if (img.alt) {
                    textContent += ` 图片描述: ${img.alt}`;
                }
            });

            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(textContent);
                speechSynthesis.speak(utterance);
            } else {
                alert('您的浏览器不支持语音合成功能。');
            }
        }

        // 新增博客添加逻辑
        const addButton = document.getElementById('addButton');
        const contentPreview = document.getElementById('contentPreview');
        const closePreview = document.getElementById('closePreview');
        const previewBlogInput = document.getElementById('previewBlogInput');
        const previewBlogContent = document.getElementById('previewBlogContent');
        const previewAddButton = document.getElementById('previewAddButton');

        addButton.addEventListener('click', () => {
            contentPreview.classList.remove('visually-hidden');
            previewBlogInput.value = '';
            previewBlogContent.value = '';
            document.getElementById('operationType').value = 'add';
            document.getElementById('blogId').value = '';
        });

        previewAddButton.addEventListener('click', () => {
            const operationType = document.getElementById('operationType').value;
            const blogId = parseInt(document.getElementById('blogId').value);
            const title = document.getElementById('previewBlogInput').value.trim();
            const content = document.getElementById('previewBlogContent').value.trim();

            if (title && content) {
                if (operationType === 'add') {
                    blogManager.addBlog(new Blog(blogManager.blogs.length + 1, title, content, 'daily', new Date().toISOString().split('T')[0], '匿名'), 0);
                } else if (operationType === 'edit') {
                    const blog = blogManager.blogs.find(b => b.id === blogId);
                    blog.title = title;
                    blog.content = content;
                    blogManager.showCategory(blog.category);
                }
                contentPreview.classList.add('visually-hidden');
            }
        });

        closePreview.addEventListener('click', () => {
            contentPreview.classList.add('visually-hidden');
        });

        // 综合处理删除和编辑操作的防抖
        document.getElementById('blogContainer').addEventListener('click', debounce((e) => {
            if (e.target.classList.contains('deleteButton')) {
                e.target.closest('.blog-card').remove();
                blogManager.announceContentChange('已删除博客内容');
            } else if (e.target.classList.contains('editButton')) {
                const blogCard = e.target.closest('.blog-card');
                const blogId = parseInt(blogCard.querySelector('h2').id.split('-')[1]);
                const blog = blogManager.blogs.find(b => b.id === blogId);

                document.getElementById('operationType').value = 'edit';
                document.getElementById('blogId').value = blogId;

                document.getElementById('previewBlogInput').value = blog.title;
                document.getElementById('previewBlogContent').value = blog.content;

                contentPreview.classList.remove('visually-hidden');
            }
        }, 300));

        // 防抖函数
        function debounce(func, delay) {
            let timeoutId;
            return function (...args) {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => func.apply(this, args), delay);
            };
        }

        // 将搜索逻辑移到防抖函数中
        const debouncedSearch = debounce((searchTerm) => {
            console.log('搜索内容:', searchTerm);

            const blogs = document.querySelectorAll('.blog-card');
            blogs.forEach(blog => {
                const blogText = blog.textContent.toLowerCase();
                blog.style.display = blogText.includes(searchTerm) ? '' : 'none';
            });
            announceChange(`搜索结果: ${searchTerm}`);
        }, 300);

        const searchInput = document.getElementById('searchInput');

        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            debouncedSearch(searchTerm);
        });

        // 窗口调整防抖
        window.addEventListener('resize', debounce(() => {
            console.log('窗口调整后的宽度:', window.innerWidth);
        }, 300));
    </script>
</body>

</html>