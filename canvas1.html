<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华丽四次贝塞尔曲线</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(45deg, #6a11cb, #2575fc);
            overflow: hidden;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            background-color: rgba(255, 255, 255, 0.1);
        }

        .control-point {
            position: absolute;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);
        }

        .control-point1 {
            background: linear-gradient(45deg, #ff7e5f, #feb47b);
        }

        .control-point2 {
            background: linear-gradient(45deg, #00c6ff, #0072ff);
        }

        .control-point3 {
            background: linear-gradient(45deg, #00ff99, #00cc66);
        }

        .control-point4 {
            background: linear-gradient(45deg, #ff6a00, #ff6a6a);
        }
    </style>
</head>

<body>
    <canvas id="bezierCanvas" width="600" height="600"></canvas>

    <div id="point1" class="control-point control-point1"></div>
    <div id="point2" class="control-point control-point2"></div>
    <div id="point3" class="control-point control-point3"></div>
    <div id="point4" class="control-point control-point4"></div>

    <script>
        const canvas = document.getElementById('bezierCanvas');
        const ctx = canvas.getContext('2d');

        // 控制点位置
        const p0 = { x: 100, y: 500 };
        const p1 = { x: 200, y: 100 };
        const p2 = { x: 400, y: 100 };
        const p3 = { x: 500, y: 500 };

        // 控制点 DOM 元素
        const point1 = document.getElementById('point1');
        const point2 = document.getElementById('point2');
        const point3 = document.getElementById('point3');
        const point4 = document.getElementById('point4');

        // 动画更新控制点位置
        function updateControlPoints() {
            point1.style.left = `${p1.x - 7}px`;
            point1.style.top = `${p1.y - 7}px`;
            point2.style.left = `${p2.x - 7}px`;
            point2.style.top = `${p2.y - 7}px`;
            point3.style.left = `${p3.x - 7}px`;
            point3.style.top = `${p3.y - 7}px`;
            point4.style.left = `${p0.x - 7}px`;
            point4.style.top = `${p0.y - 7}px`;
        }

        // 绘制贝塞尔曲线
        function drawBezierCurve() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制起点和终点
            ctx.beginPath();
            ctx.arc(p0.x, p0.y, 8, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();

            ctx.beginPath();
            ctx.arc(p3.x, p3.y, 8, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();

            // 绘制控制点
            ctx.beginPath();
            ctx.arc(p1.x, p1.y, 8, 0, 2 * Math.PI);
            ctx.fillStyle = '#ff7e5f';
            ctx.fill();

            ctx.beginPath();
            ctx.arc(p2.x, p2.y, 8, 0, 2 * Math.PI);
            ctx.fillStyle = '#00c6ff';
            ctx.fill();

            // 绘制贝塞尔曲线
            ctx.beginPath();
            ctx.moveTo(p0.x, p0.y);
            ctx.bezierCurveTo(p1.x, p1.y, p2.x, p2.y, p3.x, p3.y);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.lineWidth = 4;
            ctx.lineJoin = 'round';
            ctx.stroke();

            // 绘制控制线
            ctx.beginPath();
            ctx.moveTo(p0.x, p0.y);
            ctx.lineTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.lineTo(p3.x, p3.y);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.setLineDash([5, 5]);
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        // 动画循环函数
        function animate() {
            drawBezierCurve();
            updateControlPoints();
            requestAnimationFrame(animate);
        }

        // 启动动画
        animate();
    </script>
</body>

</html>