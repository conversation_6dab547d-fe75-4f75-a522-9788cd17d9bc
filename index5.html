<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript学习博客</title>
    <style>
        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        /* 导航栏样式 */
        header {
            background-color: #2c3e50;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            color: #fff;
            font-size: 1.5rem;
            font-weight: bold;
        }

        #mainNav ul {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        #mainNav li a {
            color: #fff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        #mainNav li a:hover {
            background-color: #34495e;
        }

        /* 主要内容区域 */
        main {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 7fr 3fr;
            gap: 2rem;
        }

        /* 文章样式 */
        .blog-post {
            background: #fff;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .blog-post h2 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .post-meta {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            display: flex;
            gap: 1rem;
        }

        .post-content {
            margin-bottom: 2rem;
        }

        .code-block {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            font-family: monospace;
        }

        /* 交互按钮样式 */
        .interaction-buttons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .interaction-buttons button {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .like-btn {
            background-color: #e74c3c;
            color: white;
        }

        .comment-btn {
            background-color: #3498db;
            color: white;
        }

        /* 打赏区域样式 */
        .donate-section {
            background: #fff;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .donate-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1rem;
        }

        #donateAmount {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        #donateButton {
            background-color: #27ae60;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        #donateButton:hover {
            background-color: #219a52;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            main {
                grid-template-columns: 1fr;
            }

            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            #mainNav ul {
                flex-direction: column;
                text-align: center;
            }
        }

        .payment-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .payment-btn {
            flex: 1;
            padding: 0.8rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1rem;
            transition: opacity 0.3s;
        }

        .payment-btn:hover {
            opacity: 0.9;
        }

        .payment-btn.wechat {
            background-color: #07c160;
            color: white;
        }

        .payment-btn.alipay {
            background-color: #1677ff;
            color: white;
        }

        .payment-icon {
            width: 24px;
            height: 24px;
            background-size: contain;
            background-repeat: no-repeat;
        }

        .wechat-icon {
            background-image: url('data:image/svg+xml,<svg t="1709799047827" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4278"><path d="M692.313043 347.269565c11.130435 0 22.26087 0 33.391305 2.226087C696.765217 204.8 540.93913 95.721739 362.852174 95.721739c-198.121739 0-360.626087 133.565217-360.626087 302.191304 0 97.947826 53.426087 178.643478 142.469565 242.086957l-35.617391 106.852174 124.434783-62.46087c44.521739 8.904348 80.139130 17.808696 124.434782 17.808696 11.130435 0 22.26087 0 33.391305-2.226087-6.678261-24.486957-11.130435-48.973913-11.130435-75.686956 0-157.426087 135.791304-284.382609 311.652174-284.382609z m-195.895652-97.947826c26.713043 0 44.521739 17.808696 44.521739 44.521739 0 26.713043-17.808696 44.521739-44.521739 44.521739-26.713043 0-53.426087-17.808696-53.426087-44.521739 0-26.713043 26.713043-44.521739 53.426087-44.521739z m-249.321739 89.043478c-26.713043 0-53.426087-17.808696-53.426087-44.521739 0-26.713043 26.713043-44.521739 53.426087-44.521739 26.713043 0 44.521739 17.808696 44.521739 44.521739 0 26.713043-17.808696 44.521739-44.521739 44.521739z m872.626087 415.721739c0-142.469565-142.469565-258.226087-302.747826-258.226087-169.73913 0-302.747826 115.756522-302.747826 258.226087 0 142.469565 133.008696 258.226087 302.747826 258.226087 35.617391 0 71.234783-8.904348 106.852174-17.808696l97.947826 53.426087-26.713044-89.043478c71.234783-53.426087 124.66087-124.434783 124.66087-204.8z m-400.695652-44.521739c-17.808696 0-35.617391-17.808696-35.617391-35.617391 0-17.808696 17.808696-35.617391 35.617391-35.617392 26.713043 0 44.521739 17.808696 44.521739 35.617392 0 17.808696-17.808696 35.617391-44.521739 35.617391z m195.895652 0c-17.808696 0-35.617391-17.808696-35.617391-35.617391 0-17.808696 17.808696-35.617391 35.617391-35.617392 26.713043 0 44.521739 17.808696 44.521739 35.617392 0 17.808696-17.808696 35.617391-44.521739 35.617391z" fill="%23ffffff" p-id="4279"></path></svg>');
        }

        .alipay-icon {
            background-image: url('data:image/svg+xml,<svg t="1709799125324" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5431"><path d="M1023.795 853.64v6.348a163.807 163.807 0 0 1-163.807 163.807h-696.18A163.807 163.807 0 0 1 0 859.988v-696.18A163.807 163.807 0 0 1 163.807 0h696.181a163.807 163.807 0 0 1 163.807 163.807V853.64z" fill="%23009FE9" p-id="5432"></path><path d="M844.836 648.267c-40.952-14.333-95.623-34.809-156.846-57.128a949.058 949.058 0 0 0 90.094-222.573H573.325V307.14h245.711v-43.41l-245.71 2.458V143.33H472.173c-18.223 0-21.704 20.476-21.704 20.476v102.38H204.759v40.952h245.71v61.427H245.712v40.952h409.518a805.522 805.522 0 0 1-64.909 148.246c-128.384-42.795-266.186-77.604-354.233-55.08a213.564 213.564 0 0 0-112.003 63.27c-95.418 116.917-26.21 294.034 175.274 294.034 119.989 0 236.087-67.366 325.771-177.73 134.322 65.932 398.666 176.297 398.666 176.297V701.3s-32.352-4.095-178.96-53.033z m-563.702 144.97c-158.893 0-204.759-124.699-126.336-194.112a191.86 191.86 0 0 1 90.913-46.276c93.575-10.238 189.811 35.629 293.624 86.614-74.941 94.598-166.674 153.774-258.2 153.774z" fill="%23FFFFFF" p-id="5433"></path></svg>');
        }

        .amount-selection {
            margin-bottom: 1rem;
        }
        
        .amount-selection select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            color: #333;
            background-color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23333' d='M6 8.825L1.175 4 2.238 2.938 6 6.7l3.763-3.762L10.825 4z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            padding-right: 2.5rem;
        }
        
        .amount-selection select:hover {
            border-color: #3498db;
        }
        
        .amount-selection select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .custom-amount-wrapper {
            position: relative;
            margin-top: 0.8rem;
        }
        
        .custom-amount-wrapper::before {
            content: "¥";
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }
        
        .custom-amount-input {
            width: 100%;
            padding: 0.8rem 1rem 0.8rem 2.2rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            color: #333;
            background-color: #fff;
            transition: all 0.3s ease;
        }
        
        .custom-amount-input:hover {
            border-color: #3498db;
        }
        
        .custom-amount-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .custom-amount-input::placeholder {
            color: #999;
        }
        
        .amount-hint {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.5rem;
            padding-left: 0.5rem;
        }
    </style>
</head>

<body>
    <header>
        <div class="nav-container">
            <div class="logo">JavaScript学习笔记</div>
            <nav id="mainNav">
                <ul id="menuItems"></ul>
            </nav>
        </div>
    </header>

    <main>
        <article class="blog-post">
            <h2>JavaScript异步编程详解</h2>
            <div class="post-meta">
                <span id="publishDate"></span>
                <span id="readCount">阅读次数：0</span>
            </div>
            <div class="post-content">
                <h3>1. 什么是异步编程？</h3>
                <p>异步编程是JavaScript中的重要概念，它允许程序在执行长时间操作时不会被阻塞。</p>
                
                <div class="code-block">
                    <pre>
// 异步函数示例
async function fetchData() {
    try {
        const response = await fetch('https://api.example.com/data');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('数据获取失败:', error);
    }
}</pre>
                </div>

                <h3>2. Promise的使用</h3>
                <p>Promise是现代JavaScript中处理异步操作的基础。</p>
                
                <div class="code-block">
                    <pre>
const promise = new Promise((resolve, reject) => {
    setTimeout(() => {
        resolve('操作成功！');
    }, 1000);
});</pre>
                </div>
            </div>
            <div class="interaction-buttons">
                <button class="like-btn" onclick="likePost()">
                    点赞 <span id="likeCount">0</span>
                </button>
                <button class="comment-btn" onclick="showComments()">查看评论</button>
            </div>
        </article>

        <aside>
            <div class="donate-section">
                <h2>支持作者</h2>
                <p>如果这篇文章对您有帮助，欢迎打赏支持！</p>
                <div class="donate-options">
                    <div class="amount-selection">
                        <select id="donateAmount" onchange="handleAmountChange(this.value)">
                            <option value="6.66">6.66 元 - 一杯奶茶</option>
                            <option value="16.66">16.66 元 - 一顿午餐</option>
                            <option value="66.66">66.66 元 - 一本好书</option>
                            <option value="custom">自定义金额</option>
                        </select>
                        <div id="customAmountContainer" style="display: none;">
                            <div class="custom-amount-wrapper">
                                <input 
                                    type="number" 
                                    id="customAmount" 
                                    class="custom-amount-input"
                                    min="0.01" 
                                    step="0.01" 
                                    placeholder="请输入打赏金额"
                                >
                            </div>
                            <div class="amount-hint">感谢您的支持，金额不限 ❤️</div>
                        </div>
                    </div>
                    <div class="payment-buttons">
                        <button id="wechatPayBtn" class="payment-btn wechat">
                            <i class="payment-icon wechat-icon"></i>
                            微信支付
                        </button>
                        <button id="alipayBtn" class="payment-btn alipay">
                            <i class="payment-icon alipay-icon"></i>
                            支付宝
                        </button>
                    </div>
                </div>
            </div>
        </aside>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = ['首页', '文章列表', '学习资源', '关于作者'];
            const ul = document.getElementById('menuItems');
            
            menuItems.forEach(item => {
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = '#';
                a.textContent = item;
                li.appendChild(a);
                ul.appendChild(li);
            });

            const publishDate = document.getElementById('publishDate');
            publishDate.textContent = `发布于 ${new Date().toLocaleDateString('zh-CN')}`;
        });

        let likeCount = 0;
        function likePost() {
            likeCount++;
            document.getElementById('likeCount').textContent = likeCount;
            
            localStorage.setItem('blogLikes', likeCount);
        }

        async function showComments() {
            try {
                const response = await fetch('https://api.example.com/comments');
                const comments = await response.json();
                console.log('评论加载成功');
            } catch (error) {
                console.error('加载评论失败:', error);
            }
        }

        // 获取支付按钮元素
        const wechatPayBtn = document.getElementById("wechatPayBtn");
        const alipayBtn = document.getElementById("alipayBtn");
        const donateAmount = document.getElementById("donateAmount");

        function createPaymentDialog(type, amount) {
            const isWechat = type === 'wechat';
            const color = isWechat ? '#07c160' : '#1677ff';
            const title = isWechat ? '微信支付' : '支付宝支付';
            const icon = isWechat ? 
                '<svg viewBox="0 0 24 24" width="36" height="36"><path d="M9.5,4C5.36,4,2,7.36,2,11.5c0,2.82,1.56,5.28,3.88,6.54L4.5,21.5l3.46-2.08c0.47,0.13,0.97,0.2,1.54,0.2c4.14,0,7.5-3.36,7.5-7.5S13.64,4,9.5,4z" fill="#07c160"/></svg>' :
                '<svg viewBox="0 0 24 24" width="36" height="36"><path d="M21.5,12c0,5.25-4.25,9.5-9.5,9.5S2.5,17.25,2.5,12S6.75,2.5,12,2.5S21.5,6.75,21.5,12z" fill="#1677ff"/></svg>';
            
            const mockPaymentDialog = document.createElement('div');
            mockPaymentDialog.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 40px;
                border-radius: 16px;
                box-shadow: 0 4px 24px rgba(0,0,0,0.15);
                z-index: 1000;
                text-align: center;
                min-width: 320px;
                max-width: 90%;
            `;
            
            mockPaymentDialog.innerHTML = `
                <div style="position: relative;">
                    <div style="position: absolute; right: -20px; top: -20px; cursor: pointer;" onclick="closePaymentDialog(this)">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2">
                            <path d="M18 6L6 18M6 6l12 12"></path>
                        </svg>
                    </div>
                    <div style="margin-bottom: 24px;">
                        <div style="width: 64px; height: 64px; background: ${color}; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px;">
                            ${icon}
                        </div>
                        <h3 style="margin: 0; color: #333; font-size: 20px;">${title}</h3>
                    </div>
                    <div style="background: #f8f9fa; padding: 16px; border-radius: 12px; margin-bottom: 24px;">
                        <p style="margin: 0 0 8px; color: #666; font-size: 14px;">支付金额</p>
                        <p style="margin: 0; font-size: 28px; font-weight: bold; color: #333;">
                            <span style="font-size: 20px;">¥</span>${amount}
                        </p>
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <button onclick="simulatePayment(this)" style="flex: 1; padding: 12px; border: none; background: ${color}; color: white; border-radius: 8px; cursor: pointer; font-size: 16px; transition: all 0.3s ease;">
                            确认支付
                        </button>
                        <button onclick="closePaymentDialog(this)" style="flex: 1; padding: 12px; border: 1px solid #ddd; background: white; color: #666; border-radius: 8px; cursor: pointer; font-size: 16px; transition: all 0.3s ease;">
                            取消
                        </button>
                    </div>
                </div>
            `;

            return mockPaymentDialog;
        }

        // 添加处理金额变化的函数
        function handleAmountChange(value) {
            const customAmountContainer = document.getElementById('customAmountContainer');
            const customAmount = document.getElementById('customAmount');
            
            if (value === 'custom') {
                customAmountContainer.style.display = 'block';
                customAmount.focus();
            } else {
                customAmountContainer.style.display = 'none';
            }
        }

        // 修改支付按钮的事件处理函数
        wechatPayBtn.addEventListener("click", () => {
            let amount;
            if (donateAmount.value === 'custom') {
                const customAmount = document.getElementById('customAmount');
                if (!customAmount.value || isNaN(customAmount.value) || customAmount.value <= 0) {
                    alert('请输入有效的金额！');
                    return;
                }
                amount = parseFloat(customAmount.value).toFixed(2);
            } else {
                amount = donateAmount.value;
            }
            showPaymentDialog('wechat', amount);
        });

        alipayBtn.addEventListener("click", () => {
            let amount;
            if (donateAmount.value === 'custom') {
                const customAmount = document.getElementById('customAmount');
                if (!customAmount.value || isNaN(customAmount.value) || customAmount.value <= 0) {
                    alert('请输入有效的金额！');
                    return;
                }
                amount = parseFloat(customAmount.value).toFixed(2);
            } else {
                amount = donateAmount.value;
            }
            showPaymentDialog('alipay', amount);
        });

        // 显示支付弹窗的函数
        function showPaymentDialog(type, amount) {
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.6);
                z-index: 999;
                backdrop-filter: blur(2px);
            `;
            
            const dialog = createPaymentDialog(type, amount);
            
            document.body.appendChild(overlay);
            document.body.appendChild(dialog);
            
            overlay.onclick = () => closePaymentDialog(dialog);
        }

        // 修改关闭支付弹窗函数
        function closePaymentDialog(element) {
            const dialog = element.closest('div[style*="position: fixed"]');
            const overlay = document.querySelector('div[style*="backdrop-filter"]');
            if (dialog) dialog.remove();
            if (overlay) overlay.remove();
            showPaymentResult(false);
        }

        // 修改 simulatePayment 函数
        function simulatePayment(btn) {
            const dialog = btn.closest('div[style*="position: fixed"]');
            const isWechat = dialog.querySelector('h3').textContent === '微信支付';
            const amount = dialog.querySelector('p[style*="font-size: 28px"]').textContent.trim();
            
            btn.innerHTML = '<span style="display: inline-block; width: 12px; height: 12px; border: 2px solid #fff; border-top: 2px solid transparent; border-radius: 50%; margin-right: 8px; animation: spin 1s linear infinite;"></span>确认中...';
            btn.disabled = true;
            btn.style.opacity = '0.7';
            
            setTimeout(() => {
                dialog.remove();
                showPaymentConfirmation(amount, isWechat);
            }, 1000);
        }

        // 修改 showPaymentConfirmation 函数
        function showPaymentConfirmation(amount, isWechat) {
            const color = isWechat ? '#07c160' : '#1677ff';
            const title = isWechat ? '微信支付' : '支付宝支付';
            const qrIcon = isWechat ? 
                `<svg t="1709799047827" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="48" height="48">
                    <path d="M692.313043 347.269565c11.130435 0 22.26087 0 33.391305 2.226087C696.765217 204.8 540.93913 95.721739 362.852174 95.721739c-198.121739 0-360.626087 133.565217-360.626087 302.191304 0 97.947826 53.426087 178.643478 142.469565 242.086957l-35.617391 106.852174 124.434783-62.46087c44.521739 8.904348 80.139130 17.808696 124.434782 17.808696 11.130435 0 22.26087 0 33.391305-2.226087-6.678261-24.486957-11.130435-48.973913-11.130435-75.686956 0-157.426087 135.791304-284.382609 311.652174-284.382609z" fill="${color}"/>
                </svg>` :
                `<svg t="1709799125324" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="48" height="48">
                    <path d="M1023.795 853.64v6.348a163.807 163.807 0 0 1-163.807 163.807h-696.18A163.807 163.807 0 0 1 0 859.988v-696.18A163.807 163.807 0 0 1 163.807 0h696.181a163.807 163.807 0 0 1 163.807 163.807V853.64z" fill="${color}"/>
                </svg>`;

            const confirmDialog = document.createElement('div');
            confirmDialog.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 16px;
                box-shadow: 0 4px 24px rgba(0,0,0,0.15);
                z-index: 1000;
                text-align: center;
                min-width: 320px;
                max-width: 90%;
            `;
            
            confirmDialog.innerHTML = `
                <div style="position: relative;">
                    <div style="position: absolute; right: -20px; top: -20px; cursor: pointer;" onclick="handlePaymentCancel()">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2">
                            <path d="M18 6L6 18M6 6l12 12"></path>
                        </svg>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <div style="width: 64px; height: 64px; margin: 0 auto 16px;">
                            ${qrIcon}
                        </div>
                        <h3 style="margin: 0; color: #333; font-size: 20px;">${title}</h3>
                        <p style="margin: 8px 0 0; color: #666; font-size: 14px;">请使用${isWechat ? '微信' : '支付宝'}扫码完成支付</p>
                    </div>
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=payment_confirmation_${isWechat ? 'wechat' : 'alipay'}" 
                         alt="支付二维码" 
                         style="width: 200px; height: 200px; margin: 10px auto; display: block; padding: 10px; border: 1px solid #eee; border-radius: 8px;">
                    <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <p style="margin: 0; color: #666; font-size: 14px;">支付金额</p>
                        <p style="margin: 5px 0; font-size: 24px; font-weight: bold; color: #333;">${amount}</p>
                    </div>
                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button onclick="handlePaymentConfirm()" style="flex: 1; padding: 12px; border: none; background: ${color}; color: white; border-radius: 8px; cursor: pointer; font-size: 16px; transition: all 0.3s ease;">
                            确认已完成支付
                        </button>
                        <button onclick="handlePaymentCancel()" style="flex: 1; padding: 12px; border: 1px solid #ddd; background: white; color: #666; border-radius: 8px; cursor: pointer; font-size: 16px; transition: all 0.3s ease;">
                            取消支付
                        </button>
                    </div>
                </div>
            `;

            // 添加遮罩层
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.6);
                z-index: 999;
                backdrop-filter: blur(2px);
            `;
            
            document.body.appendChild(overlay);
            document.body.appendChild(confirmDialog);
        }

        function handlePaymentConfirm() {
            const confirmDialog = document.querySelector('div[style*="position: fixed"]');
            confirmDialog.innerHTML = `
                <div style="text-align: center;">
                    <div style="margin: 20px 0;">
                        <div style="border: 3px solid #f3f3f3; border-top: 3px solid #07c160; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                    </div>
                    <p style="color: #666; margin: 15px 0;">正在处理支付请求...</p>
                </div>
            `;
            
            setTimeout(() => {
                showPaymentResult(true);
            }, 2000);
        }

        function handlePaymentCancel() {
            showPaymentResult(false);
        }

        function showPaymentResult(success) {
            // 移除之前的弹窗和遮罩
            const existingDialogs = document.querySelectorAll('div[style*="position: fixed"]');
            existingDialogs.forEach(dialog => dialog.remove());
            const overlay = document.querySelector('div[style*="backdrop-filter"]');
            if (overlay) overlay.remove();
            
            const resultToast = document.createElement('div');
            resultToast.style.cssText = `
                position: fixed;
                top: 20%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 24px 40px;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.12);
                z-index: 1000;
                text-align: center;
                min-width: 280px;
                animation: slideDown 0.3s ease;
                display: flex;
                align-items: center;
                gap: 12px;
            `;
            
            if (success) {
                resultToast.innerHTML = `
                    <div style="width: 40px; height: 40px; background: #07c160; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <svg viewBox="0 0 24 24" width="24" height="24" fill="white">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                        </svg>
                    </div>
                    <div>
                        <p style="margin: 0; color: #333; font-size: 18px; font-weight: 500;">支付成功</p>
                        <p style="margin: 4px 0 0; color: #666; font-size: 14px;">感谢您的支持！</p>
                    </div>
                `;
            } else {
                resultToast.innerHTML = `
                    <div style="width: 40px; height: 40px; background: #ff4d4f; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <svg viewBox="0 0 24 24" width="24" height="24" fill="white">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                        </svg>
                    </div>
                    <div>
                        <p style="margin: 0; color: #333; font-size: 18px; font-weight: 500;">支付取消</p>
                        <p style="margin: 4px 0 0; color: #666; font-size: 14px;">您已取消本次支付</p>
                    </div>
                `;
            }
            
            document.body.appendChild(resultToast);
            
            setTimeout(() => {
                resultToast.style.transition = 'all 0.3s ease';
                resultToast.style.opacity = '0';
                resultToast.style.transform = 'translate(-50%, -100%)';
                setTimeout(() => resultToast.remove(), 300);
            }, 2000);
        }
    </script>
</body>

</html>