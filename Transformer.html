<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>Transformer 架构演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            max-width: 900px;
            margin: auto;
        }

        .step {
            border-left: 5px solid #007acc;
            background: #f9f9f9;
            margin: 20px 0;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .step h2 {
            color: #007acc;
        }

        .highlight {
            color: #d63384;
            font-weight: bold;
        }

        .token-box,
        .embedding-box,
        .attention-matrix,
        .multihead-box,
        .ffn-box,
        .norm-box {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .token,
        .vector,
        .weight,
        .head,
        .ffn,
        .norm {
            padding: 10px;
            border-radius: 5px;
            background: #e0eaff;
            font-family: monospace;
            text-align: center;
        }

        .vector,
        .weight,
        .head,
        .ffn,
        .norm {
            background: #d1f1ff;
        }
    </style>
</head>

<body>
    <h1>Transformer 架构逐步可视化演示</h1>

    <div class="step" id="tokenization">
        <h2>Step 1：Tokenization 分词</h2>
        <p>输入句子：<span class="highlight">"小明昨天去图书馆，他借了一本书。"</span></p>
        <p>分词结果：</p>
        <div class="token-box" id="tokens"></div>
    </div>

    <div class="step" id="embedding">
        <h2>Step 2：Embedding 向量表示</h2>
        <p>每个 token 映射为一个向量（随机演示）</p>
        <div class="embedding-box" id="embeddings"></div>
    </div>

    <div class="step" id="attention">
        <h2>Step 3：Self-Attention 自注意力</h2>
        <p>我们模拟"他"关注其他词的注意力分数：</p>
        <div class="attention-matrix" id="attention"></div>
    </div>

    <div class="step" id="multihead">
        <h2>Step 4：Multi-Head Attention 多头注意力</h2>
        <p>每个头可以捕捉不同类型的语义关系：</p>
        <div class="multihead-box" id="heads"></div>
    </div>

    <div class="step" id="ffn">
        <h2>Step 5：Feed Forward 前馈网络</h2>
        <p>每个词向量通过小型神经网络进行非线性转换：</p>
        <div class="ffn-box" id="ffns"></div>
    </div>

    <div class="step" id="norm">
        <h2>Step 6：LayerNorm & Residual 归一化与残差连接</h2>
        <p>确保数值稳定并保留原始信息：</p>
        <div class="norm-box" id="norms"></div>
    </div>

    <script>
        const tokens = ["小", "明", "昨天", "去", "图书馆", "，", "他", "借", "了", "一本", "书", "。"];
        const tokenBox = document.getElementById("tokens");
        const embedBox = document.getElementById("embeddings");
        const attentionBox = document.getElementById("attention");
        const headBox = document.getElementById("heads");
        const ffnBox = document.getElementById("ffns");
        const normBox = document.getElementById("norms");

        // Step 1: Tokenization
        tokens.forEach(t => {
            const div = document.createElement("div");
            div.className = "token";
            div.innerText = t;
            tokenBox.appendChild(div);
        });

        // Step 2: Embedding
        tokens.forEach(t => {
            const vec = Array.from({ length: 4 }, () => (Math.random() * 2 - 1).toFixed(2));
            const div = document.createElement("div");
            div.className = "vector";
            div.innerText = `${t} → [${vec.join(", ")}]`;
            embedBox.appendChild(div);
        });

        // Step 3: Self-Attention
        const attentionMap = [
            { word: "小", weight: 0.2 },
            { word: "明", weight: 0.25 },
            { word: "图书馆", weight: 0.3 },
            { word: "借", weight: 0.25 },
        ];
        attentionMap.forEach(item => {
            const div = document.createElement("div");
            div.className = "weight";
            div.innerText = `"他" → ${item.word} : 权重 ${item.weight}`;
            attentionBox.appendChild(div);
        });

        // Step 4: Multi-Head Attention
        const heads = [
            { id: 1, type: "主谓关系" },
            { id: 2, type: "指代推理" },
            { id: 3, type: "时间顺序" },
            { id: 4, type: "实体动作" },
        ];
        heads.forEach(h => {
            const div = document.createElement("div");
            div.className = "head";
            div.innerText = `Head ${h.id}：关注 ${h.type}`;
            headBox.appendChild(div);
        });

        // Step 5: Feed Forward
        tokens.forEach(t => {
            const val = (Math.random() * 2 + 1).toFixed(2);
            const div = document.createElement("div");
            div.className = "ffn";
            div.innerText = `${t} → 激活输出 ${val}`;
            ffnBox.appendChild(div);
        });

        // Step 6: LayerNorm & Residual
        tokens.forEach(t => {
            const normVal = (Math.random() * 1 + 0.5).toFixed(2);
            const div = document.createElement("div");
            div.className = "norm";
            div.innerText = `${t} → LayerNorm值 ${normVal}`;
            normBox.appendChild(div);
        });
    </script>
</body>

</html>