<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>刀锋效果</title>
    <style>
        body {
            margin: 0;
            height: 100vh;
            background-color: black;
            overflow: hidden;
            position: relative;
        }

        .blade {
            position: absolute;
            width: 150px;
            height: 5px;
            background: linear-gradient(to right, transparent, white, transparent);
            pointer-events: none;
            transform-origin: center;
            transition: opacity 0.2s ease-out;
            opacity: 0;
        }
    </style>
</head>

<body>

    <script>
        let isDragging = false; // 用于检测是否正在拖拽

        document.addEventListener("mousedown", (event) => {
            isDragging = true; // 开始拖拽
        });

        document.addEventListener("mousemove", (event) => {
            if (isDragging) {  // 只有在拖拽时才显示刀锋
                let blade = document.createElement("div");
                blade.classList.add("blade");
                document.body.appendChild(blade);

                // 设置刀锋位置
                blade.style.left = `${event.clientX - 75}px`;
                blade.style.top = `${event.clientY}px`;

                // 计算随机角度，增强切割效果
                let angle = Math.random() * 60 - 30; // -30度到30度之间
                blade.style.transform = `rotate(${angle}deg)`;

                // 让刀锋淡出并移除
                blade.style.opacity = "1";
                setTimeout(() => {
                    blade.style.opacity = "0";
                    setTimeout(() => {
                        blade.remove();
                    }, 200);
                }, 100);
            }
        });

        document.addEventListener("mouseup", () => {
            isDragging = false; // 停止拖拽
        });
    </script>

</body>

</html>