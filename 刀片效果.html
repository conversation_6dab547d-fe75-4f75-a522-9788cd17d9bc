<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>刀片切割效果</title>
    <style>
        /* 页面背景 */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: chocolate;
            color: #fff;
            overflow: hidden;
        }

        /* 刀片容器 */
        .blade {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 250px;
            height: 150px;
            pointer-events: none;
            /* 确保鼠标可以穿透 */
            animation: float 2s ease-in-out infinite;
        }

        /* 页面切割效果 */
        .cut-effect {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            clip-path: polygon(0 0, 100% 0, 100% 50%, 0 50%);
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            transition: clip-path 0.2s ease-out;
        }

        /* 刀片浮动动画 */
        @keyframes float {

            0%,
            100% {
                transform: translate(-50%, -50%) translateY(-10px);
            }

            50% {
                transform: translate(-50%, -50%) translateY(10px);
            }
        }
    </style>
</head>

<body>
    <div class="blade"></div>
    <div class="cut-effect"></div>

    <script>
        // 监听鼠标移动事件
        document.addEventListener('mousemove', (e) => {
            const blade = document.querySelector('.blade');
            const cutEffect = document.querySelector('.cut-effect');

            // 根据鼠标位置移动刀片
            blade.style.top = `${e.clientY}px`;
            blade.style.left = `${e.clientX}px`;

            // 根据鼠标位置生成页面切割效果
            cutEffect.style.clipPath = `polygon(0 0, ${e.clientX}px ${e.clientY}px, 100% 100%, 0 100%)`;
        });
    </script>
</body>

</html>