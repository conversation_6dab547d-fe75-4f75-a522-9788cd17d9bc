<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>四次贝塞尔曲线</title>
    <style>
        body {
            background: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        canvas {
            border: 1px solid #00000044;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            background: #fff;
        }
    </style>
</head>

<body>
    <canvas id="bezierCanvas" width="800" height="600"></canvas>
    <script>
        const canvas = document.getElementById("bezierCanvas");
        const ctx = canvas.getContext("2d");

        let points = [
            { x: 100, y: 500, color: "red" },
            { x: 250, y: 100, color: "blue" },
            { x: 400, y: 550, color: "green" },
            { x: 550, y: 50, color: "yellow" },
            { x: 700, y: 500, color: "purple" }
        ];
        let draggingPoint = null;

        function drawBezierCurve() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制控制点
            points.forEach(point => {
                ctx.beginPath();
                ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
                ctx.fillStyle = point.color;
                ctx.fill();
            });

            // 绘制控制点连线
            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);
            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y);
            }
            ctx.strokeStyle = "rgba(0, 0, 0, 0.3)";
            ctx.setLineDash([5, 5]);
            ctx.stroke();
            ctx.setLineDash([]);

            // 绘制四次贝塞尔曲线
            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);
            ctx.strokeStyle = "#0000FF";
            ctx.lineWidth = 3;

            for (let t = 0; t <= 1; t += 0.01) {
                let x = Math.pow(1 - t, 4) * points[0].x +
                    4 * Math.pow(1 - t, 3) * t * points[1].x +
                    6 * Math.pow(1 - t, 2) * Math.pow(t, 2) * points[2].x +
                    4 * (1 - t) * Math.pow(t, 3) * points[3].x +
                    Math.pow(t, 4) * points[4].x;

                let y = Math.pow(1 - t, 4) * points[0].y +
                    4 * Math.pow(1 - t, 3) * t * points[1].y +
                    6 * Math.pow(1 - t, 2) * Math.pow(t, 2) * points[2].y +
                    4 * (1 - t) * Math.pow(t, 3) * points[3].y +
                    Math.pow(t, 4) * points[4].y;

                ctx.lineTo(x, y);
            }
            ctx.stroke();
        }

        function getMousePos(evt) {
            const rect = canvas.getBoundingClientRect();
            return {
                x: evt.clientX - rect.left,
                y: evt.clientY - rect.top
            };
        }

        canvas.addEventListener("mousedown", (evt) => {
            const mousePos = getMousePos(evt);
            points.forEach(point => {
                const dx = mousePos.x - point.x;
                const dy = mousePos.y - point.y;
                if (dx * dx + dy * dy < 36) {
                    draggingPoint = point;
                }
            });
        });

        canvas.addEventListener("mousemove", (evt) => {
            if (draggingPoint) {
                const mousePos = getMousePos(evt);
                draggingPoint.x = mousePos.x;
                draggingPoint.y = mousePos.y;
                drawBezierCurve();
            }
        });

        canvas.addEventListener("mouseup", () => {
            draggingPoint = null;
        });

        drawBezierCurve();
    </script>
</body>

</html>