<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My First Blog</title>
    <style>
        /* 全局样式 */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Arial';
            line-height: 1.6;
            color: #333;
            background-color: #acacac;
        }

        .container {
            width: 80%;
            margin: 0 auto;
            overflow: hidden;
        }

        /* 头部样式 */
        header {
            background: linear-gradient(to right, #3ad594, #00ffff);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        header h1 {
            margin: 0;
            font-size: 2.5rem;
        }

        nav ul {
            list-style: none;
            padding-top: 1rem;
        }

        nav ul li {
            display: inline;
            margin-right: 1rem;
        }

        nav ul li a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        nav ul li a:hover {
            color: #ffd700;
        }

        /* 主要内容区样式 */
        main {
            display: flex;
            /* 使用 flex 布局，使文章可以灵活排列 */
            flex-wrap: wrap;
            justify-content: space-between;
            padding: 2rem 0;
        }

        .blog-post {
            flex-basis: calc(50% - 1rem);
            /* 两列布局，每个盒子占50%宽度，减去间距 */
            background: white;
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .blog-post:hover {
            transform: translateY(-5px);
        }

        .blog-post h2 {
            color: #3ad594;
            margin-bottom: 0.5rem;
        }

        .blog-post .meta {
            color: #777;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .blog-post p {
            margin-bottom: 1rem;
        }

        .read-more {
            display: inline-block;
            background-color: #3ad594;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        .read-more:hover {
            background-color: #2ca340;
        }

        /* 页脚样式 */
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 1rem 0;
            margin-top: 2rem;
        }

        /* 响应式设计部分 */
        @media (max-width: 768px) {

            /* 当屏幕宽度小于768px时，转换为单列布局 */
            .blog-post {
                flex-basis: 100%;
                /* 盒模型占据整行宽度 */
                margin-bottom: 1rem;
                /* 缩小文章之间的间距 */
            }

            header h1 {
                font-size: 2rem;
                /* 调整标题大小以适应较小的屏幕 */
            }

            .container {
                width: 90%;
                /* 缩小 container 的宽度以适应较小设备 */
            }
        }

        @media (max-width: 480px) {

            /* 当屏幕宽度小于480px时，进一步调整布局 */
            .read-more {
                padding: 0.4rem 0.8rem;
                /* 调整按钮的填充 */
                font-size: 0.9rem;
                /* 缩小按钮文字的大小 */
            }

            nav ul {
                text-align: center;
            }

            nav ul li {
                display: block;
                /* 导航项变为垂直排列 */
                margin-bottom: 0.5rem;
            }

            header {
                padding: 0.8rem 0;
                /* 缩小头部的内边距 */
            }

            header h1 {
                font-size: 1.8rem;
                /* 进一步缩小标题 */
            }
        }
    </style>
</head>

<body>
    <header>
        <div class="container">
            <h1>我的第一个博客</h1>
            <nav>
                <ul>
                    <li><a href="#">首页</a></li>
                    <li><a href="#">文章</a></li>
                    <li><a href="#">关于</a></li>
                    <li><a href="#">联系</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <article class="blog-post">
            <h2>创新的力量</h2>
            <div class="meta">发布于: 20xx年x月xx日 | 作者: 史蒂夫·乔布斯</div>
            <p>"创新就是与众不同。"这句简短而有力的名言揭示了创新的本质。在科技领域，敢于打破常规、推陈出新往往能带来突破性的发展。</p>
            <a href="#" class="read-more">阅读更多</a>
        </article>

        <article class="blog-post">
            <h2>科技与未来</h2>
            <div class="meta">发布于: 20xx年x月xx日 | 作者: 比尔·盖茨</div>
            <p>"我们总是高估了未来两年的变化，却低估了未来十年的变化。"这句话提醒我们要用长远的眼光看待科技发展，保持耐心和持续的创新精神。</p>
            <a href="#" class="read-more">阅读更多</a>
        </article>

        <article class="blog-post">
            <h2>突破思维局限</h2>
            <div class="meta">发布于: 19xx年x月xx日 | 作者: 阿尔伯特·爱因斯坦</div>
            <p>"想象力比知识更重要。知识是有限的，而想象力概括着世界的一切，推动着进步，并且是知识进化的源泉。"在科技创新中，突破性思维常常源于大胆的想象。</p>
            <a href="#" class="read-more">阅读更多</a>
        </article>

        <article class="blog-post">
            <h2>坚持与毅力</h2>
            <div class="meta">发布于: 19xx年x月xx日 | 作者: 托马斯·爱迪生</div>
            <p>"天才是1%的灵感加上99%的汗水。"这句名言强调了在科技创新过程中坚持不懈的重要性。伟大的发明往往需要长期的努力和无数次的尝试。</p>
            <a href="#" class="read-more">阅读更多</a>
        </article>

        <article id="blog-post01" class="blog-post">
            <h2>通往形上而学的捷径</h2>
            <div class="meta">发布于: 1855年7月14日 | 作者: “Punch”, London humor magazine</div>
            <p>What is Matter?—Never mind.<br> What is Mind?—No matter.</p>
            <a href="#" class="read-more">阅读更多</a>
        </article>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 我的个人博客. 保留所有权利.</p>
        </div>
    </footer>
</body>

</html>