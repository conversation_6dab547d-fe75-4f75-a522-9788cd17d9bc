window.addEventListener('DOMContentLoaded', () => {
    const donationCanvas = document.getElementById('donationCanvas');
    const ctx = donationCanvas.getContext('2d');

    // 设置 Canvas 尺寸
    donationCanvas.width = 300;
    donationCanvas.height = 220;

    // 清除背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, donationCanvas.width, donationCanvas.height);

    // 添加标题
    ctx.fillStyle = '#333';
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('支持作者', donationCanvas.width / 2, 30);

    // 添加说明文字
    ctx.font = '14px Arial';
    ctx.fillStyle = '#666';
    ctx.fillText('如果这篇文章对您有帮助，欢迎打赏支持！', donationCanvas.width / 2, 60);

    // 创建输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.placeholder = '请输入金额';
    input.style.position = 'absolute';
    input.style.left = `${donationCanvas.offsetLeft + 50}px`;
    input.style.top = `${donationCanvas.offsetTop + 80}px`;
    input.style.width = '200px';
    input.style.height = '30px';
    input.style.border = '1px solid #ccc';
    input.style.borderRadius = '5px';
    input.style.textAlign = 'center';
    input.style.fontSize = '14px';

    document.body.appendChild(input);

    // 画支付按钮
    ctx.fillStyle = '#1AAD19';
    ctx.fillRect(50, 130, 100, 40);
    ctx.fillStyle = '#fff';
    ctx.fillText('微信支付', 100, 155);
    ctx.fillStyle = '#007AFF';
    ctx.fillRect(160, 130, 100, 40);
    ctx.fillStyle = '#fff';
    ctx.fillText('支付宝', 210, 155);

    // 监听支付按钮点击事件
    donationCanvas.addEventListener('click', (event) => {
        const x = event.offsetX;
        const y = event.offsetY;
        let paymentType = '';

        if (x >= 50 && x <= 150 && y >= 130 && y <= 170) {
            paymentType = '微信支付';
        }
        if (x >= 160 && x <= 260 && y >= 130 && y <= 170) {
            paymentType = '支付宝';
        }

        if (paymentType) {
            const amount = input.value.trim();
            if (!amount) {
                alert('请输入金额！');
                return;
            }

            showPaymentModal(paymentType, amount);
        }
    });

    function showPaymentModal(paymentType, amount) {
        const modal = document.createElement('div');
        modal.style.position = 'fixed';
        modal.style.top = '50%';
        modal.style.left = '50%';
        modal.style.transform = 'translate(-50%, -50%)';
        modal.style.width = '300px';
        modal.style.height = '350px';
        modal.style.backgroundColor = '#fff';
        modal.style.borderRadius = '10px';
        modal.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.2)';
        modal.style.textAlign = 'center';
        modal.style.padding = '20px';
        modal.style.zIndex = '1000';

        const title = document.createElement('h3');
        title.innerText = paymentType;
        title.style.marginBottom = '10px';

        const amountText = document.createElement('p');
        amountText.innerText = `支付金额：¥${amount}`;
        amountText.style.fontSize = '16px';
        amountText.style.marginBottom = '10px';

        const qrCode = document.createElement('img');
        qrCode.src = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${Math.random()}`;
        qrCode.style.width = '150px';
        qrCode.style.height = '150px';
        qrCode.style.position = 'relative';

        const scanCanvas = document.createElement('canvas');
        scanCanvas.width = 150;
        scanCanvas.height = 150;
        scanCanvas.style.position = 'absolute';
        scanCanvas.style.left = '50%';
        scanCanvas.style.top = '50%';
        scanCanvas.style.transform = 'translate(-50%, -50%)';
        scanCanvas.style.pointerEvents = 'none'; // 确保扫描动画不会阻挡二维码的点击事件

        const scanCtx = scanCanvas.getContext('2d');
        let scanY = 0;

        function animateScanLine() {
            scanCtx.clearRect(0, 0, scanCanvas.width, scanCanvas.height);
            const gradient = scanCtx.createLinearGradient(0, scanY, 0, scanY + 10);
            gradient.addColorStop(0, 'rgba(0,255,0,0.8)');
            gradient.addColorStop(1, 'rgba(0,255,0,0)');
            scanCtx.fillStyle = gradient;
            scanCtx.fillRect(0, scanY, scanCanvas.width, 10);

            scanY += 2;
            if (scanY > scanCanvas.height) {
                scanY = 0;
            }
            requestAnimationFrame(animateScanLine);
        }
        animateScanLine();

        const closeButton = document.createElement('button');
        closeButton.innerText = '我已支付';
        closeButton.style.marginTop = '10px';
        closeButton.style.padding = '10px 20px';
        closeButton.style.border = 'none';
        closeButton.style.backgroundColor = '#007AFF';
        closeButton.style.color = '#fff';
        closeButton.style.borderRadius = '5px';
        closeButton.style.cursor = 'pointer';

        closeButton.addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        const qrWrapper = document.createElement('div');
        qrWrapper.style.position = 'relative';
        qrWrapper.style.width = '150px';
        qrWrapper.style.height = '150px';
        qrWrapper.style.margin = 'auto';

        qrWrapper.appendChild(qrCode);
        qrWrapper.appendChild(scanCanvas);
        modal.appendChild(title);
        modal.appendChild(amountText);
        modal.appendChild(qrWrapper);
        modal.appendChild(closeButton);

        document.body.appendChild(modal);
    }
});
