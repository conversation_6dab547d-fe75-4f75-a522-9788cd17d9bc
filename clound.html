<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>Canvas 绘制腾讯云 Logo</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #ffffff;
            margin: 0;
        }

        canvas {
            border: 1px solid #ccc;
        }
    </style>
</head>

<body>
    <canvas id="logoCanvas" width="600" height="300"></canvas>
    <script>
        const canvas = document.getElementById("logoCanvas");
        const ctx = canvas.getContext("2d");

        // 设定线条样式
        ctx.lineWidth = 10;
        ctx.lineCap = "round";

        // 画左侧云部分（青色）
        ctx.strokeStyle = "#00A0E9";
        ctx.beginPath();
        ctx.moveTo(200, 180);
        ctx.quadraticCurveTo(120, 100, 200, 80);
        ctx.quadraticCurveTo(280, 60, 320, 100);
        ctx.stroke();

        // 画右侧云部分（深蓝色）
        ctx.strokeStyle = "#0078FF";
        ctx.beginPath();
        ctx.moveTo(320, 100);
        ctx.quadraticCurveTo(400, 140, 360, 180);
        ctx.quadraticCurveTo(280, 260, 200, 180);
        ctx.stroke();

        // 绘制“腾讯云”文字
        ctx.fillStyle = "#000";
        ctx.font = "bold 48px Arial";
        ctx.fillText("腾讯云", 380, 200);
    </script>
</body>

</html>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>Canvas 绘制腾讯云 Logo</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #ffffff;
            margin: 0;
        }

        canvas {
            border: 1px solid #ccc;
        }
    </style>
</head>

<body>
    <canvas id="logoCanvas" width="600" height="300"></canvas>
    <script>
        const canvas = document.getElementById("logoCanvas");
        const ctx = canvas.getContext("2d");

        // 设定线条样式
        ctx.lineWidth = 10;
        ctx.lineCap = "round";

        // 绘制云形轮廓
        ctx.strokeStyle = "#0078FF";
        ctx.beginPath();
        ctx.moveTo(200, 180);
        ctx.quadraticCurveTo(150, 100, 250, 80);
        ctx.quadraticCurveTo(300, 40, 350, 80);
        ctx.quadraticCurveTo(450, 100, 400, 180);
        ctx.quadraticCurveTo(350, 250, 250, 220);
        ctx.quadraticCurveTo(180, 200, 200, 180);
        ctx.stroke();

        // 绘制“腾讯云”文字
        ctx.fillStyle = "#000";
        ctx.font = "bold 48px Arial";
        ctx.fillText("腾讯云", 380, 200);
    </script>
</body>

</html>