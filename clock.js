window.addEventListener('DOMContentLoaded', () => {
    const clockCanvas = document.getElementById('clockCanvas');
    const ctx = clockCanvas.getContext('2d');

    let speedMultiplier = 1; // 初始速度倍率，长按时修改
    let isPressing = false;

    function drawClock() {
        const now = new Date();
        const seconds = now.getSeconds() * speedMultiplier;
        const minutes = now.getMinutes() * speedMultiplier;
        const hours = now.getHours() * speedMultiplier;

        ctx.clearRect(0, 0, clockCanvas.width, clockCanvas.height);

        // 绘制时钟背景
        ctx.beginPath();
        ctx.arc(75, 75, 70, 0, Math.PI * 2);
        ctx.fillStyle = '#f9f9f9';
        ctx.fill();
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.stroke();

        // 添加阴影效果
        ctx.shadowColor = "rgba(0, 0, 0, 0.3)";
        ctx.shadowBlur = 6;
        ctx.shadowOffsetX = 4;
        ctx.shadowOffsetY = 4;

        // 绘制时针
        ctx.beginPath();
        ctx.moveTo(75, 75);
        ctx.lineTo(
            75 + 30 * Math.sin((hours % 12) * Math.PI / 6 + minutes * Math.PI / (6 * 60)),
            75 - 30 * Math.cos((hours % 12) * Math.PI / 6 + minutes * Math.PI / (6 * 60))
        );
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 4;
        ctx.stroke();

        // 绘制分针
        ctx.beginPath();
        ctx.moveTo(75, 75);
        ctx.lineTo(
            75 + 50 * Math.sin(minutes * Math.PI / 30),
            75 - 50 * Math.cos(minutes * Math.PI / 30)
        );
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 3;
        ctx.stroke();

        // 绘制秒针
        ctx.beginPath();
        ctx.moveTo(75, 75);
        ctx.lineTo(
            75 + 60 * Math.sin(seconds * Math.PI / 30),
            75 - 60 * Math.cos(seconds * Math.PI / 30)
        );
        ctx.strokeStyle = '#ff0000';
        ctx.lineWidth = 2;
        ctx.stroke();

        // 绘制中心点
        ctx.beginPath();
        ctx.arc(75, 75, 5, 0, Math.PI * 2);
        ctx.fillStyle = '#000';
        ctx.fill();

        // 清除阴影
        ctx.shadowColor = "transparent";

        requestAnimationFrame(drawClock);
    }

    // 监听鼠标/触摸事件
    clockCanvas.addEventListener('mousedown', () => startPressing());
    clockCanvas.addEventListener('mouseup', () => stopPressing());
    clockCanvas.addEventListener('mouseleave', () => stopPressing());

    clockCanvas.addEventListener('touchstart', (e) => {
        e.preventDefault(); // 阻止默认行为
        startPressing();
    });
    clockCanvas.addEventListener('touchend', () => stopPressing());
    clockCanvas.addEventListener('touchcancel', () => stopPressing());

    function startPressing() {
        if (!isPressing) {
            isPressing = true;
            speedMultiplier = 10; // 加快速度
        }
    }

    function stopPressing() {
        isPressing = false;
        speedMultiplier = 1; // 恢复正常速度
    }

    drawClock();
});